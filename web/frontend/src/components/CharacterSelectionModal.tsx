import React, { useState } from 'react'
import { Modal, <PERSON>, Card, Avatar, Button, Typography, Tag, Space, Spin, Empty, message } from 'antd'
import { UserOutlined, RobotOutlined, CheckCircleOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { useGetWorldCharactersQuery, useUpdateCharacterMutation } from '../store/api'
import type { GameWorld, GameCharacter } from '../store/slices/gameSlice'

const { Title, Text, Paragraph } = Typography

// 样式组件
const StyledModal = styled(Modal)`
  .ant-modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
  }
`

const CharacterCard = styled(Card)`
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  &.selected {
    border-color: #52c41a;
    background-color: #f6ffed;
  }

  .ant-card-body {
    padding: 16px;
  }

  .character-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .character-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .character-description {
    color: #666;
    margin-bottom: 8px;
  }

  .character-traits {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
`

const HeaderSection = styled.div`
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;

  .title {
    color: white !important;
    margin-bottom: 8px;
  }

  .description {
    color: rgba(255, 255, 255, 0.9);
  }
`

interface CharacterSelectionModalProps {
  world: GameWorld | null
  visible: boolean
  onClose: () => void
  onCharacterSelected: (character: GameCharacter) => void
}

/**
 * 角色选择模态框组件
 * 允许玩家从世界中的NPC中选择一个作为可操控角色
 */
const CharacterSelectionModal: React.FC<CharacterSelectionModalProps> = ({
  world,
  visible,
  onClose,
  onCharacterSelected
}) => {
  const [selectedCharacter, setSelectedCharacter] = useState<GameCharacter | null>(null)
  const [loading, setLoading] = useState(false)

  // 获取世界中的NPC角色
  const { 
    data: npcCharactersData, 
    isLoading: npcCharactersLoading 
  } = useGetWorldCharactersQuery({
    worldId: world?.id || '',
    page: 1,
    limit: 50,
    character_type: 'npc'
  }, {
    skip: !world?.id || !visible
  })

  const [updateCharacter] = useUpdateCharacterMutation()

  if (!world) {
    return null
  }

  const handleSelectCharacter = (character: GameCharacter) => {
    setSelectedCharacter(character)
  }

  const handleConfirmSelection = async () => {
    if (!selectedCharacter) {
      message.warning('请先选择一个角色')
      return
    }

    setLoading(true)
    try {
      // 将选中的NPC转换为玩家角色
      await updateCharacter({
        characterId: selectedCharacter.id,
        data: {
          character_type: 'player'
        }
      }).unwrap()

      message.success(`成功选择角色：${selectedCharacter.name}`)
      onCharacterSelected(selectedCharacter)
      onClose()
    } catch (error: any) {
      console.error('选择角色失败:', error)
      message.error(error?.data?.message || '选择角色失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setSelectedCharacter(null)
    onClose()
  }

  const npcCharacters = npcCharactersData?.data?.items || []

  return (
    <StyledModal
      title={null}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button 
          key="confirm" 
          type="primary" 
          loading={loading}
          disabled={!selectedCharacter}
          onClick={handleConfirmSelection}
        >
          选择这个角色
        </Button>
      ]}
      width={700}
      centered
    >
      <HeaderSection>
        <Title level={3} className="title">选择你的角色</Title>
        <Paragraph className="description">
          从 {world.name} 世界中选择一个NPC角色作为你的可操控角色。
          选择后，该角色将成为你在这个世界中的化身。
        </Paragraph>
      </HeaderSection>

      {npcCharactersLoading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, color: '#666' }}>正在加载角色列表...</div>
        </div>
      ) : npcCharacters.length === 0 ? (
        <Empty 
          description="该世界暂无可选择的NPC角色"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Text type="secondary">
            请联系世界创建者添加NPC角色，或选择其他世界进行游戏。
          </Text>
        </Empty>
      ) : (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              共找到 {npcCharacters.length} 个可选择的角色，点击选择你喜欢的角色：
            </Text>
          </div>
          
          {npcCharacters.map((character) => (
            <CharacterCard 
              key={character.id}
              className={selectedCharacter?.id === character.id ? 'selected' : ''}
              onClick={() => handleSelectCharacter(character)}
              size="small"
            >
              <div className="character-header">
                <div className="character-info">
                  <Avatar 
                    icon={<RobotOutlined />} 
                    size="large"
                  />
                  <div>
                    <Space>
                      <Text strong style={{ fontSize: '16px' }}>{character.name}</Text>
                      <Tag color="blue">NPC</Tag>
                      {selectedCharacter?.id === character.id && (
                        <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
                      )}
                    </Space>
                  </div>
                </div>
              </div>
              
              {character.description && (
                <div className="character-description">
                  <Paragraph ellipsis={{ rows: 2 }}>
                    {character.description}
                  </Paragraph>
                </div>
              )}
              
              {character.traits && character.traits.length > 0 && (
                <div className="character-traits">
                  <Text type="secondary" style={{ marginRight: 8 }}>特质:</Text>
                  {character.traits.slice(0, 5).map((trait, index) => (
                    <Tag key={index} size="small">{trait}</Tag>
                  ))}
                  {character.traits.length > 5 && (
                    <Tag size="small">+{character.traits.length - 5}</Tag>
                  )}
                </div>
              )}
            </CharacterCard>
          ))}
        </div>
      )}
    </StyledModal>
  )
}

export default CharacterSelectionModal
