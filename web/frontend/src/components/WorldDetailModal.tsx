import React, { useState } from 'react'
import { Modal, Tabs, Card, List, Avatar, Tag, Typography, Space, Spin, Empty, Button } from 'antd'
import { 
  UserOutlined, 
  EnvironmentOutlined, 
  ClockCircleOutlined,
  TeamOutlined,
  RobotOutlined,
  PlayCircleOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { useGetWorldCharactersQuery } from '../store/api'
import type { GameWorld, GameCharacter } from '../store/slices/gameSlice'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

// 样式组件
const StyledModal = styled(Modal)`
  .ant-modal-body {
    padding: 24px;
  }
`

const WorldHeader = styled.div`
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;

  .world-title {
    color: white !important;
    margin-bottom: 8px;
  }

  .world-description {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
  }

  .world-stats {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
  }
`

const CharacterCard = styled(Card)`
  margin-bottom: 12px;
  
  .ant-card-body {
    padding: 16px;
  }

  .character-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .character-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .character-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .character-traits {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }
`

interface WorldDetailModalProps {
  world: GameWorld | null
  visible: boolean
  onClose: () => void
  onJoinWorld?: (world: GameWorld) => void
}

/**
 * 世界详情模态框组件
 * 显示世界的详细信息，包括基本信息、NPC列表、玩家列表等
 */
const WorldDetailModal: React.FC<WorldDetailModalProps> = ({
  world,
  visible,
  onClose,
  onJoinWorld
}) => {
  const [activeTab, setActiveTab] = useState('info')

  // 获取世界中的角色数据
  const { 
    data: allCharactersData, 
    isLoading: allCharactersLoading 
  } = useGetWorldCharactersQuery({
    worldId: world?.id || '',
    page: 1,
    limit: 50
  }, {
    skip: !world?.id || !visible
  })

  const { 
    data: npcCharactersData, 
    isLoading: npcCharactersLoading 
  } = useGetWorldCharactersQuery({
    worldId: world?.id || '',
    page: 1,
    limit: 50,
    character_type: 'npc'
  }, {
    skip: !world?.id || !visible
  })

  const { 
    data: playerCharactersData, 
    isLoading: playerCharactersLoading 
  } = useGetWorldCharactersQuery({
    worldId: world?.id || '',
    page: 1,
    limit: 50,
    character_type: 'player'
  }, {
    skip: !world?.id || !visible
  })

  if (!world) {
    return null
  }

  const handleJoinWorld = () => {
    if (onJoinWorld) {
      onJoinWorld(world)
    }
    onClose()
  }

  const renderCharacterList = (characters: GameCharacter[], loading: boolean, emptyText: string) => {
    if (loading) {
      return <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '40px' }} />
    }

    if (!characters || characters.length === 0) {
      return (
        <Empty 
          description={emptyText}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )
    }

    return (
      <div>
        {characters.map((character) => (
          <CharacterCard key={character.id} size="small">
            <div className="character-header">
              <div className="character-info">
                <Avatar 
                  icon={character.characterType === 'npc' ? <RobotOutlined /> : <UserOutlined />} 
                  size="small"
                />
                <div>
                  <Text strong>{character.name}</Text>
                  <Tag 
                    color={character.characterType === 'npc' ? 'blue' : 'green'}
                    size="small"
                    style={{ marginLeft: 8 }}
                  >
                    {character.characterType === 'npc' ? 'NPC' : '玩家'}
                  </Tag>
                </div>
              </div>
              {character.user && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {character.user.displayName || character.user.username}
                </Text>
              )}
            </div>
            
            {character.description && (
              <div className="character-description">
                {character.description}
              </div>
            )}
            
            {character.traits && character.traits.length > 0 && (
              <div className="character-traits">
                {character.traits.map((trait, index) => (
                  <Tag key={index} size="small">{trait}</Tag>
                ))}
              </div>
            )}
          </CharacterCard>
        ))}
      </div>
    )
  }

  return (
    <StyledModal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button 
          key="join" 
          type="primary" 
          icon={<PlayCircleOutlined />}
          onClick={handleJoinWorld}
        >
          加入世界
        </Button>
      ]}
      width={800}
      centered
    >
      <WorldHeader>
        <Title level={2} className="world-title">{world.name}</Title>
        <Paragraph className="world-description">
          {world.description}
        </Paragraph>
        <div className="world-stats">
          <div className="stat-item">
            <TeamOutlined />
            <span>{world.currentPlayers}/{world.maxPlayers} 玩家</span>
          </div>
          <div className="stat-item">
            <EnvironmentOutlined />
            <span>{world.theme}</span>
          </div>
          <div className="stat-item">
            <ClockCircleOutlined />
            <span>{new Date(world.createdAt).toLocaleDateString()}</span>
          </div>
        </div>
      </WorldHeader>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="基本信息" key="info">
          <Card>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Text strong>世界名称：</Text>
                <Text>{world.name}</Text>
              </div>
              <div>
                <Text strong>世界主题：</Text>
                <Tag color="blue">{world.theme}</Tag>
              </div>
              <div>
                <Text strong>世界描述：</Text>
                <Paragraph>{world.description}</Paragraph>
              </div>
              <div>
                <Text strong>玩家容量：</Text>
                <Text>{world.currentPlayers}/{world.maxPlayers}</Text>
              </div>
              <div>
                <Text strong>公开状态：</Text>
                <Tag color={world.isPublic ? 'green' : 'orange'}>
                  {world.isPublic ? '公开' : '私有'}
                </Tag>
              </div>
              <div>
                <Text strong>创建时间：</Text>
                <Text>{new Date(world.createdAt).toLocaleString()}</Text>
              </div>
            </Space>
          </Card>
        </TabPane>

        <TabPane tab={`NPC角色 (${npcCharactersData?.data?.total || 0})`} key="npcs">
          {renderCharacterList(
            npcCharactersData?.data?.items || [],
            npcCharactersLoading,
            '该世界暂无NPC角色'
          )}
        </TabPane>

        <TabPane tab={`玩家角色 (${playerCharactersData?.data?.total || 0})`} key="players">
          {renderCharacterList(
            playerCharactersData?.data?.items || [],
            playerCharactersLoading,
            '该世界暂无玩家角色'
          )}
        </TabPane>
      </Tabs>
    </StyledModal>
  )
}

export default WorldDetailModal
